import torch
import torch.nn as nn
import torch.optim as optim
import torchvision
import torchvision.transforms as transforms
import time
from wide_resnet_original import wide_resnet_28_10

def test_subset_sizes():
    """Test different subset sizes to find the best balance"""
    
    print("Testing Different CIFAR-10 Subset Sizes")
    print("="*50)
    
    # Different subset fractions to test
    subset_fractions = [0.001, 0.005, 0.01, 0.02, 0.05]  # 0.1%, 0.5%, 1%, 2%, 5%
    
    for fraction in subset_fractions:
        print(f"\nTesting with {fraction*100:.1f}% of CIFAR-10...")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            # Setup data
            transform = transforms.Compose([
                transforms.ToTensor(),
                transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
            ])
            
            # Load full dataset
            full_trainset = torchvision.datasets.CIFAR10(root='./data', train=True, 
                                                         download=True, transform=transform)
            full_testset = torchvision.datasets.CIFAR10(root='./data', train=False, 
                                                        download=False, transform=transform)
            
            # Create subset
            train_size = int(len(full_trainset) * fraction)
            test_size = int(len(full_testset) * fraction)
            
            train_size = max(train_size, 10)  # Minimum 10 samples
            test_size = max(test_size, 10)
            
            train_indices = torch.randperm(len(full_trainset))[:train_size]
            test_indices = torch.randperm(len(full_testset))[:test_size]
            
            trainset = torch.utils.data.Subset(full_trainset, train_indices)
            testset = torch.utils.data.Subset(full_testset, test_indices)
            
            # Create data loaders
            batch_size = min(32, train_size)
            trainloader = torch.utils.data.DataLoader(trainset, batch_size=batch_size, shuffle=True)
            testloader = torch.utils.data.DataLoader(testset, batch_size=batch_size, shuffle=False)
            
            print(f"Dataset sizes: Train={len(trainset)}, Test={len(testset)}")
            print(f"Batches: Train={len(trainloader)}, Test={len(testloader)}")
            
            # Quick training test
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            model = wide_resnet_28_10(num_classes=10, input_channels=3).to(device)
            
            criterion = nn.CrossEntropyLoss()
            optimizer = optim.SGD(model.parameters(), lr=0.1, momentum=0.9)
            
            # Train for 2 epochs
            model.train()
            for epoch in range(2):
                running_loss = 0.0
                for batch_idx, (data, target) in enumerate(trainloader):
                    data, target = data.to(device), target.to(device)
                    
                    optimizer.zero_grad()
                    output = model(data)
                    loss = criterion(output, target)
                    loss.backward()
                    optimizer.step()
                    
                    running_loss += loss.item()
                
                avg_loss = running_loss / len(trainloader)
                print(f"  Epoch {epoch+1}: Loss = {avg_loss:.4f}")
            
            # Test
            model.eval()
            correct = 0
            total = 0
            with torch.no_grad():
                for data, target in testloader:
                    data, target = data.to(device), target.to(device)
                    outputs = model(data)
                    _, predicted = torch.max(outputs, 1)
                    total += target.size(0)
                    correct += (predicted == target).sum().item()
            
            accuracy = 100 * correct / total
            elapsed_time = time.time() - start_time
            
            print(f"  Test Accuracy: {accuracy:.2f}%")
            print(f"  Training Time: {elapsed_time:.2f}s")
            
            # Memory usage (approximate)
            if torch.cuda.is_available():
                memory_used = torch.cuda.max_memory_allocated() / 1024**2  # MB
                print(f"  GPU Memory Used: {memory_used:.1f} MB")
                torch.cuda.reset_peak_memory_stats()
            
        except Exception as e:
            print(f"  Error: {e}")
        
        print("-" * 40)

def recommend_subset_size():
    """Recommend optimal subset size based on system"""
    print("\nRecommendations:")
    print("="*30)
    
    # Check available memory
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        print(f"GPU Memory: {gpu_memory:.1f} GB")
        
        if gpu_memory >= 8:
            recommended = 0.05  # 5%
        elif gpu_memory >= 4:
            recommended = 0.02  # 2%
        else:
            recommended = 0.01  # 1%
    else:
        print("Using CPU")
        recommended = 0.01  # 1%
    
    print(f"Recommended subset: {recommended*100:.1f}% of CIFAR-10")
    
    # Calculate actual sizes
    train_size = int(50000 * recommended)  # CIFAR-10 has 50k training samples
    test_size = int(10000 * recommended)   # CIFAR-10 has 10k test samples
    
    print(f"This means: {train_size} training samples, {test_size} test samples")
    print(f"Training will be approximately {100/recommended:.0f}x faster!")

if __name__ == "__main__":
    print("CIFAR-10 Subset Size Tester")
    print("This will help you find the optimal subset size for your system")
    print("="*60)
    
    # Test different sizes
    test_subset_sizes()
    
    # Give recommendations
    recommend_subset_size()
    
    print("\nTo use your preferred subset size, modify train1.py:")
    print("model, results = train_model(subset_fraction=0.01)  # Change 0.01 to your preferred fraction")
