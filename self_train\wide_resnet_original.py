import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.nn.init as init
from torch.autograd import Variable
import numpy as np


def conv3x3(in_planes, out_planes, stride=1):
    """3x3 convolution with padding"""
    return nn.Conv2d(in_planes, out_planes, kernel_size=3, stride=stride, padding=1, bias=True)


class WideBasicBlock(nn.Module):
    """Wide Basic Block for Wide ResNet"""
    
    def __init__(self, in_planes, planes, dropout_rate, stride=1):
        super(WideBasicBlock, self).__init__()
        self.bn1 = nn.BatchNorm2d(in_planes)
        self.conv1 = nn.Conv2d(in_planes, planes, kernel_size=3, padding=1, bias=True)
        self.dropout = nn.Dropout(p=dropout_rate)
        self.bn2 = nn.BatchNorm2d(planes)
        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3, stride=stride, padding=1, bias=True)
        
        self.shortcut = nn.Sequential()
        if stride != 1 or in_planes != planes:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_planes, planes, kernel_size=1, stride=stride, bias=True),
            )

    def forward(self, x):
        out = self.dropout(self.conv1(F.relu(self.bn1(x))))
        out = self.conv2(F.relu(self.bn2(out)))
        out += self.shortcut(x)
        return out


class WideResNet(nn.Module):
    """
    Wide Residual Network
    
    Args:
        depth (int): Total number of layers. Should be 6n+4 (e.g., 16, 22, 28, 34, 40)
        widen_factor (int): Width multiplier for channels (e.g., 1, 2, 4, 8, 10)
        dropout_rate (float): Dropout probability
        num_classes (int): Number of output classes
        input_channels (int): Number of input channels (3 for RGB, 1 for grayscale)
    
    Popular configurations:
        - WRN-28-10: depth=28, widen_factor=10
        - WRN-16-8: depth=16, widen_factor=8
        - WRN-40-4: depth=40, widen_factor=4
    """
    
    def __init__(self, depth, widen_factor, dropout_rate, num_classes=10, input_channels=3):
        super(WideResNet, self).__init__()
        self.in_planes = 16

        assert ((depth - 4) % 6 == 0), 'Wide-ResNet depth should be 6n+4'
        n = (depth - 4) // 6
        k = widen_factor

        print(f'| Wide-ResNet {depth}x{k}')
        
        # Channel progression: [16, 16*k, 32*k, 64*k]
        nStages = [16, 16*k, 32*k, 64*k]

        # Initial convolution
        self.conv1 = conv3x3(input_channels, nStages[0])
        
        # Three groups of wide basic blocks
        self.layer1 = self._wide_layer(WideBasicBlock, nStages[1], n, dropout_rate, stride=1)
        self.layer2 = self._wide_layer(WideBasicBlock, nStages[2], n, dropout_rate, stride=2)
        self.layer3 = self._wide_layer(WideBasicBlock, nStages[3], n, dropout_rate, stride=2)
        
        # Final batch norm and classifier
        self.bn1 = nn.BatchNorm2d(nStages[3], momentum=0.9)
        self.linear = nn.Linear(nStages[3], num_classes)
        
        # Initialize weights
        self._initialize_weights()

    def _wide_layer(self, block, planes, num_blocks, dropout_rate, stride):
        """Create a wide layer with multiple blocks"""
        strides = [stride] + [1] * (int(num_blocks) - 1)
        layers = []

        for stride in strides:
            layers.append(block(self.in_planes, planes, dropout_rate, stride))
            self.in_planes = planes

        return nn.Sequential(*layers)

    def _initialize_weights(self):
        """Initialize network weights"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                init.constant_(m.weight, 1)
                init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                init.normal_(m.weight, 0, 0.01)
                init.constant_(m.bias, 0)

    def forward(self, x):
        out = self.conv1(x)
        out = self.layer1(out)
        out = self.layer2(out)
        out = self.layer3(out)
        out = F.relu(self.bn1(out))
        out = F.avg_pool2d(out, 8)
        out = out.view(out.size(0), -1)
        out = self.linear(out)
        return out


def wide_resnet_28_10(num_classes=10, input_channels=3, dropout_rate=0.3):
    """Wide ResNet 28-10 (most popular configuration)"""
    return WideResNet(depth=28, widen_factor=10, dropout_rate=dropout_rate, 
                     num_classes=num_classes, input_channels=input_channels)


def wide_resnet_16_8(num_classes=10, input_channels=3, dropout_rate=0.3):
    """Wide ResNet 16-8"""
    return WideResNet(depth=16, widen_factor=8, dropout_rate=dropout_rate,
                     num_classes=num_classes, input_channels=input_channels)


def wide_resnet_40_4(num_classes=10, input_channels=3, dropout_rate=0.3):
    """Wide ResNet 40-4"""
    return WideResNet(depth=40, widen_factor=4, dropout_rate=dropout_rate,
                     num_classes=num_classes, input_channels=input_channels)


def wide_resnet_22_8(num_classes=10, input_channels=3, dropout_rate=0.3):
    """Wide ResNet 22-8"""
    return WideResNet(depth=22, widen_factor=8, dropout_rate=dropout_rate,
                     num_classes=num_classes, input_channels=input_channels)


if __name__ == '__main__':
    # Test the model
    print("Testing Wide ResNet models...")
    
    # Test WRN-28-10 (most common)
    net = wide_resnet_28_10(num_classes=10, input_channels=3)
    x = torch.randn(2, 3, 32, 32)  # Batch of 2 CIFAR-10 images
    y = net(x)
    print(f"WRN-28-10 output shape: {y.shape}")
    
    # Test WRN-16-8
    net2 = wide_resnet_16_8(num_classes=100, input_channels=3)  # CIFAR-100
    y2 = net2(x)
    print(f"WRN-16-8 output shape: {y2.shape}")
    
    # Count parameters
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"WRN-28-10 parameters: {count_parameters(net):,}")
    print(f"WRN-16-8 parameters: {count_parameters(net2):,}")
