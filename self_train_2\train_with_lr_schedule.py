import torch
import torch.nn as nn
import torch.optim as optim
import torchvision
import torchvision.transforms as transforms
import time
import os
import json
import numpy as np
import math
from datetime import datetime
from wide_resnet_original import wide_resnet_28_10

def learning_rate_schedule(init_lr, epoch):
    """Learning rate schedule from the TensorFlow implementation"""
    optim_factor = 0
    if epoch > 90:
        optim_factor = 3
    elif epoch > 60:
        optim_factor = 2
    elif epoch > 30:
        optim_factor = 1
    
    return init_lr * math.pow(0.2, optim_factor)

def format_time(seconds):
    """Format time in a readable way"""
    if seconds < 60:
        return f"{seconds:.2f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"

def setup_data_loaders(batch_size=100, data_path='./data'):
    """Setup CIFAR-10 data loaders matching the TensorFlow implementation"""
    print("Setting up CIFAR-10 data loaders...")
    
    # CIFAR-10 normalization (same as TensorFlow version)
    transform_train = transforms.Compose([
        transforms.RandomCrop(32, padding=4),
        transforms.RandomHorizontalFlip(),
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
    ])

    transform_test = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
    ])

    try:
        trainset = torchvision.datasets.CIFAR10(root=data_path, train=True,
                                               download=True, transform=transform_train)
        testset = torchvision.datasets.CIFAR10(root=data_path, train=False,
                                              download=False, transform=transform_test)

        trainloader = torch.utils.data.DataLoader(
            trainset, batch_size=batch_size, shuffle=True, num_workers=2
        )
        testloader = torch.utils.data.DataLoader(
            testset, batch_size=batch_size, shuffle=False, num_workers=2
        )

    except Exception as e:
        print(f"Error loading CIFAR-10 data: {e}")
        raise

    print(f"Training set size: {len(trainset)}")
    print(f"Test set size: {len(testset)}")
    print(f"Batch size: {batch_size}")
    
    return trainloader, testloader

def train_model_with_schedule(epochs=120, batch_size=100, init_lr=0.1, save_results=True):
    """Train model with learning rate schedule from TensorFlow implementation"""
    print("="*60)
    print("TRAINING WITH LEARNING RATE SCHEDULE")
    print("="*60)

    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Setup data
    trainloader, testloader = setup_data_loaders(batch_size)

    # Setup model (matching TensorFlow version: depth=28, widen_factor=10, dropout=0.3)
    model = wide_resnet_28_10(num_classes=10, input_channels=3, dropout_rate=0.3).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    
    print(f"Model: Wide ResNet 28-10")
    print(f"Total parameters: {total_params:,}")
    print(f"Initial learning rate: {init_lr}")
    print(f"Training for {epochs} epochs")
    print("-"*60)

    # Setup training
    criterion = nn.CrossEntropyLoss()
    # Note: We'll update the learning rate manually each epoch
    optimizer = optim.SGD(model.parameters(), lr=init_lr, momentum=0.9, weight_decay=5e-4)

    # Initialize results tracking
    results = {
        "model_info": {
            "architecture": "Wide ResNet 28-10",
            "total_parameters": total_params,
            "device": str(device)
        },
        "training_config": {
            "epochs": epochs,
            "batch_size": batch_size,
            "initial_learning_rate": init_lr,
            "optimizer": "SGD with LR schedule",
            "momentum": 0.9,
            "weight_decay": 5e-4
        },
        "epoch_results": [],
        "learning_rates": [],
        "training_time": 0
    }

    # Training loop
    start_time = time.time()
    model.train()

    for epoch in range(epochs):
        epoch_start_time = time.time()
        
        # Update learning rate according to schedule
        current_lr = learning_rate_schedule(init_lr, epoch + 1)
        for param_group in optimizer.param_groups:
            param_group['lr'] = current_lr
        
        running_loss = 0.0
        correct = 0
        total_samples = 0

        print(f"Epoch {epoch+1}/{epochs}, LR: {current_lr:.6f}")

        for batch_idx, (data, target) in enumerate(trainloader):
            data, target = data.to(device), target.to(device)

            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()

            # Statistics
            running_loss += loss.item()     #item: Extracts scalar value from tensor (for accumulation)
            _, predicted = torch.max(output.data, 1)       # output max posibility results?
            total_samples += target.size(0)
            correct += (predicted == target).sum().item() 

            # Print progress every 100 batches
            if batch_idx % 100 == 0:
                print(f"  Batch {batch_idx}/{len(trainloader)}, Loss: {loss.item():.4f}")

        # Epoch statistics
        epoch_time = time.time() - epoch_start_time
        epoch_loss = running_loss / len(trainloader)
        epoch_acc = 100. * correct / total_samples

        print(f"  Train Loss: {epoch_loss:.4f}")
        print(f"  Train Accuracy: {epoch_acc:.2f}%")
        print(f"  Time: {format_time(epoch_time)}")

        # Test every epoch
        test_acc = test_model(model, testloader, device, detailed=False)
        
        # Store results
        results["epoch_results"].append({
            "epoch": epoch + 1,
            "train_loss": epoch_loss,
            "train_accuracy": epoch_acc,
            "test_accuracy": test_acc,
            "learning_rate": current_lr,
            "epoch_time": epoch_time
        })
        results["learning_rates"].append(current_lr)
        
        print("-"*40)

    # Final statistics
    total_time = time.time() - start_time
    results["training_time"] = total_time

    print("="*60)
    print("TRAINING COMPLETED")
    print(f"Total training time: {format_time(total_time)}")
    print(f"Final test accuracy: {results['epoch_results'][-1]['test_accuracy']:.2f}%")
    print("="*60)

    # Save results if requested
    if save_results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"training_results_lr_schedule_{timestamp}.json"
        save_training_results(results, filename)

    return model, results

def test_model(model, testloader, device, detailed=False):
    """Test function"""
    model.eval()
    correct = 0
    total = 0
    test_loss = 0.0
    criterion = nn.CrossEntropyLoss()

    with torch.no_grad():
        for data, target in testloader:
            data, target = data.to(device), target.to(device)
            outputs = model(data)
            loss = criterion(outputs, target)
            test_loss += loss.item()

            _, predicted = torch.max(outputs, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()

    accuracy = 100 * correct / total
    avg_loss = test_loss / len(testloader)

    print(f"  Test Loss: {avg_loss:.4f}")
    print(f"  Test Accuracy: {accuracy:.2f}% ({correct}/{total})")

    return accuracy

def save_training_results(results, filename="training_results.json"):
    """Save training results to JSON file"""
    os.makedirs("./results", exist_ok=True)
    filepath = os.path.join("./results", filename)

    with open(filepath, 'w') as f:
        json.dump(results, f, indent=2)

    print(f"Results saved to: {filepath}")
    return filepath

if __name__ == "__main__":
    print("Starting Wide ResNet Training with Learning Rate Schedule")
    print("Based on the successful TensorFlow implementation")
    print("="*60)

    # Train the model with the same configuration as TensorFlow version
    model, results = train_model_with_schedule(
        epochs=120,
        batch_size=100,
        init_lr=0.1,  # Starts high but decays according to schedule
        save_results=True
    )

    print(f"\nTraining completed!")
    print(f"This should achieve ~95.56% accuracy like the TensorFlow version")
