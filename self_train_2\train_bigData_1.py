import torch
import torch.nn as nn
import torch.optim as optim
import torchvision
import torchvision.transforms as transforms
import time
import os
import json
import numpy as np
from datetime import datetime
from wide_resnet_original import wide_resnet_28_10

def format_time(seconds):
    """Format time in a readable way"""
    if seconds < 60:
        return f"{seconds:.2f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"

def display_epoch_timing(epoch_times, total_time):
    """Display detailed timing information for each epoch"""
    print("\n" + "="*60)
    print("DETAILED TIMING ANALYSIS")
    print("="*60)

    print("Epoch-by-Epoch Timing:")
    print("Epoch |   Time   | Cumulative |  % of Total")
    print("-" * 45)

    cumulative = 0
    for i, epoch_time in enumerate(epoch_times):
        cumulative += epoch_time
        percentage = (epoch_time / total_time) * 100
        print(f"{i+1:5d} | {format_time(epoch_time):8s} | {format_time(cumulative):10s} | {percentage:8.1f}%")

    print("-" * 45)
    print(f"Total | {format_time(total_time):8s} | {format_time(total_time):10s} | {100.0:8.1f}%")

    # Statistics
    avg_time = total_time / len(epoch_times)
    fastest = min(epoch_times)
    slowest = max(epoch_times)

    print(f"\nTiming Statistics:")
    print(f"  Average per epoch: {format_time(avg_time)}")
    print(f"  Fastest epoch: {format_time(fastest)} (Epoch {epoch_times.index(fastest)+1})")
    print(f"  Slowest epoch: {format_time(slowest)} (Epoch {epoch_times.index(slowest)+1})")
    print(f"  Time variation: {format_time(slowest - fastest)}")

    # Predictions
    print(f"\nTime Predictions:")
    print(f"  For 10 epochs: ~{format_time(avg_time * 10)}")
    print(f"  For 20 epochs: ~{format_time(avg_time * 20)}")
    print(f"  For 50 epochs: ~{format_time(avg_time * 50)}")

def check_data_exists(data_path='./data'):
    """Check if CIFAR-10 data exists in the specified path"""
    cifar_path = os.path.join(data_path, 'cifar-10-batches-py')

    if os.path.exists(cifar_path):
        print(f"✓ CIFAR-10 data found at: {cifar_path}")
        return True
    else:
        print(f"✗ CIFAR-10 data not found at: {cifar_path}")
        print("Expected structure: ./data/cifar-10-batches-py/")
        print("If you have the data elsewhere, please move it to ./data/ or update the path")
        return False

def setup_data_loaders(batch_size=128, data_path='./data'):
    """Setup CIFAR-10 data loaders with optimizations"""
    print("Setting up CIFAR-10 data loaders...")

    # Check if data exists first
    if not check_data_exists(data_path):
        print("Attempting to download CIFAR-10 data...")
        download = True
    else:
        download = False

    # Data preprocessing with optimizations
    transform_train = transforms.Compose([
        transforms.RandomCrop(32, padding=4),
        transforms.RandomHorizontalFlip(),
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
    ])

    transform_test = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
    ])

    try:
        # Load datasets
        trainset = torchvision.datasets.CIFAR10(root=data_path, train=True,
                                               download=download, transform=transform_train)

        # Optimized DataLoader settings
        num_workers = min(4, os.cpu_count()) if os.cpu_count() else 2
        pin_memory = torch.cuda.is_available()

        trainloader = torch.utils.data.DataLoader(
            trainset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=pin_memory,
            persistent_workers=True if num_workers > 0 else False
        )

        testset = torchvision.datasets.CIFAR10(root=data_path, train=False,
                                              download=download, transform=transform_test)
        testloader = torch.utils.data.DataLoader(
            testset,
            batch_size=100,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=pin_memory,
            persistent_workers=True if num_workers > 0 else False
        )

    except Exception as e:
        print(f"Error loading CIFAR-10 data: {e}")
        print("Please check your data path and ensure CIFAR-10 is properly downloaded")
        raise

    print(f"Training set size: {len(trainset)}")
    print(f"Test set size: {len(testset)}")
    print(f"Number of training batches: {len(trainloader)}")
    print(f"Number of test batches: {len(testloader)}")

    return trainloader, testloader

def save_training_results(results, filename="training_results.json"):
    """Save training results to JSON file"""
    os.makedirs("./results", exist_ok=True)
    filepath = os.path.join("./results", filename)

    with open(filepath, 'w') as f:
        json.dump(results, f, indent=2)

    print(f"Results saved to: {filepath}")
    return filepath

def train_model(epochs=200, batch_size=128, lr=0.1, save_results=True):
    """Enhanced training function with detailed output"""
    print("="*60)
    print("STARTING TRAINING SESSION")
    print("="*60)

    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

    # Setup data
    trainloader, testloader = setup_data_loaders(batch_size)

    # Setup model
    model = wide_resnet_28_10(num_classes=10, input_channels=3).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"Model: Wide ResNet 28-10")
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # Setup training
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.SGD(model.parameters(), lr=lr, momentum=0.9, weight_decay=5e-4)

    print(f"Optimizer: SGD (lr={lr}, momentum=0.9, weight_decay=5e-4)")
    print(f"Training for {epochs} epochs")
    print("-"*60)

    # Initialize results tracking
    results = {
        "model_info": {
            "architecture": "Wide ResNet 28-10",
            "total_parameters": total_params,
            "trainable_parameters": trainable_params,
            "device": str(device)
        },
        "training_config": {
            "epochs": epochs,
            "batch_size": batch_size,
            "learning_rate": lr,
            "optimizer": "SGD",
            "momentum": 0.9,
            "weight_decay": 5e-4
        },
        "epoch_results": [],
        "batch_losses": [],
        "training_time": 0,
        "epoch_times": []
    }

    # Training loop
    start_time = time.time()
    model.train()

    for epoch in range(epochs):
        epoch_start_time = time.time()
        running_loss = 0.0
        correct = 0
        total_samples = 0

        print(f"Epoch {epoch+1}/{epochs}")

        for batch_idx, (data, target) in enumerate(trainloader):
            data, target = data.to(device), target.to(device)

            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()

            # Statistics
            running_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            total_samples += target.size(0)
            correct += (predicted == target).sum().item()

            # Store batch loss for analysis
            results["batch_losses"].append({
                "epoch": epoch + 1,
                "batch": batch_idx,
                "loss": loss.item()
            })

            # Print progress every 100 batches
            if batch_idx % 100 == 0:
                print(f"  Batch {batch_idx}/{len(trainloader)}, Loss: {loss.item():.4f}")

        # Epoch statistics
        epoch_time = time.time() - epoch_start_time
        epoch_loss = running_loss / len(trainloader)
        epoch_acc = 100. * correct / total_samples

        # Store epoch results
        epoch_result = {
            "epoch": epoch + 1,
            "train_loss": epoch_loss,
            "train_accuracy": epoch_acc,
            "epoch_time": epoch_time
        }

        print(f"  Train Loss: {epoch_loss:.4f}")
        print(f"  Train Accuracy: {epoch_acc:.2f}%")
        print(f"  Time: {format_time(epoch_time)}")

        # Test every epoch
        test_acc = test_model(model, testloader, device)
        epoch_result["test_accuracy"] = test_acc

        results["epoch_results"].append(epoch_result)
        results["epoch_times"].append(epoch_time)
        print("-"*40)

    # Final statistics
    total_time = time.time() - start_time
    results["training_time"] = total_time

    print("="*60)
    print("TRAINING COMPLETED")
    print(f"Total training time: {format_time(total_time)}")
    print(f"Average time per epoch: {format_time(total_time/epochs)}")
    print("="*60)

    # Display detailed timing analysis
    display_epoch_timing(results["epoch_times"], total_time)

    # Save results if requested
    if save_results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"training_results_{timestamp}.json"
        save_training_results(results, filename)

    return model, results

def test_model(model, testloader, device, detailed=True):
    """Enhanced test function with detailed analysis"""
    model.eval()
    correct = 0
    total = 0
    class_correct = [0. for i in range(10)]
    class_total = [0. for i in range(10)]

    # CIFAR-10 class names
    classes = ('plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck')

    test_loss = 0.0
    criterion = nn.CrossEntropyLoss()

    with torch.no_grad():
        for data, target in testloader:
            data, target = data.to(device), target.to(device)
            outputs = model(data)
            loss = criterion(outputs, target)
            test_loss += loss.item()

            _, predicted = torch.max(outputs, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()

            # Per-class accuracy
            c = (predicted == target).squeeze()
            for i in range(target.size(0)):
                label = target[i]
                class_correct[label] += c[i].item()
                class_total[label] += 1

    accuracy = 100 * correct / total
    avg_loss = test_loss / len(testloader)

    print(f"  Test Loss: {avg_loss:.4f}")
    print(f"  Test Accuracy: {accuracy:.2f}% ({correct}/{total})")

    if detailed:
        print("  Per-class Accuracy:")
        for i in range(10):
            if class_total[i] > 0:
                class_acc = 100 * class_correct[i] / class_total[i]
                print(f"    {classes[i]}: {class_acc:.2f}% ({int(class_correct[i])}/{int(class_total[i])})")

    return accuracy

def analyze_results(results_file):
    """Analyze and display training results"""
    if not os.path.exists(results_file):
        print(f"Results file {results_file} not found!")
        return

    with open(results_file, 'r') as f:
        results = json.load(f)

    print("\n" + "="*60)
    print("TRAINING ANALYSIS")
    print("="*60)

    # Model info
    model_info = results["model_info"]
    print(f"Architecture: {model_info['architecture']}")
    print(f"Parameters: {model_info['total_parameters']:,}")
    print(f"Device: {model_info['device']}")

    # Training config
    config = results["training_config"]
    print(f"\nTraining Configuration:")
    print(f"  Epochs: {config['epochs']}")
    print(f"  Batch Size: {config['batch_size']}")
    print(f"  Learning Rate: {config['learning_rate']}")
    print(f"  Total Training Time: {results['training_time']:.2f}s")

    # Epoch results
    epoch_results = results["epoch_results"]
    print(f"\nEpoch-by-Epoch Results:")
    print("Epoch | Train Loss | Train Acc | Test Acc | Time(s)")
    print("-" * 50)
    for result in epoch_results:
        print(f"{result['epoch']:5d} | {result['train_loss']:10.4f} | {result['train_accuracy']:9.2f} | {result['test_accuracy']:8.2f} | {result['epoch_time']:7.2f}")

    # Final results
    final_result = epoch_results[-1]
    print(f"\nFinal Results:")
    print(f"  Best Train Accuracy: {max([r['train_accuracy'] for r in epoch_results]):.2f}%")
    print(f"  Best Test Accuracy: {max([r['test_accuracy'] for r in epoch_results]):.2f}%")
    print(f"  Final Train Loss: {final_result['train_loss']:.4f}")

    # Loss progression
    print(f"\nLoss Progression:")
    train_losses = [r['train_loss'] for r in epoch_results]
    print(f"  Initial Loss: {train_losses[0]:.4f}")
    print(f"  Final Loss: {train_losses[-1]:.4f}")
    print(f"  Loss Reduction: {((train_losses[0] - train_losses[-1]) / train_losses[0] * 100):.1f}%")

if __name__ == "__main__":
    print("Starting Wide ResNet Training with Detailed Output")
    print("="*60)

    # Train the model with reduced learning rate to prevent gradient explosion
    model, results = train_model(epochs=200, batch_size=128, lr=0.1, save_results=True)

    # Analyze results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"./results/training_results_{timestamp}.json"

    print("\nAnalyzing training results...")
    analyze_results(results_file)

    print(f"\nAll outputs saved to:")
    print(f"  Results: {results_file}")
    print(f"  Model can be accessed via the returned 'model' variable")

    print("\nTo get more detailed analysis, you can:")
    print("1. Check the JSON file for batch-level loss data")
    print("2. Plot training curves using the saved data")
    print("3. Analyze per-class performance")
    print("4. Compare different training runs")