import torch
import torchvision
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import numpy as np
import os

def check_cifar10_exists(data_path='./data'):
    """
    Check if CIFAR-10 dataset is already downloaded

    Args:
        data_path (str): Path to data directory

    Returns:
        bool: True if CIFAR-10 exists, False otherwise
    """
    cifar10_path = os.path.join(data_path, 'cifar-10-batches-py')

    # Check if the main directory exists
    if not os.path.exists(cifar10_path):
        return False

    # Check if all required files exist
    required_files = [
        'data_batch_1',
        'data_batch_2',
        'data_batch_3',
        'data_batch_4',
        'data_batch_5',
        'test_batch',
        'batches.meta'
    ]

    for file_name in required_files:
        file_path = os.path.join(cifar10_path, file_name)
        if not os.path.exists(file_path):
            print(f"Missing file: {file_path}")
            return False

    return True

def get_cifar10_size(data_path='./data'):
    """
    Get the size of CIFAR-10 dataset on disk

    Args:
        data_path (str): Path to data directory

    Returns:
        float: Size in MB, or 0 if not found
    """
    cifar10_path = os.path.join(data_path, 'cifar-10-batches-py')

    if not os.path.exists(cifar10_path):
        return 0

    total_size = 0
    for dirpath, dirnames, filenames in os.walk(cifar10_path):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            if os.path.exists(file_path):
                total_size += os.path.getsize(file_path)

    return total_size / (1024 * 1024)  # Convert to MB

def visualize_cifar10_samples(num_samples=10, save_path="./cifar10_samples", data_path='./data'):
    """
    Extract and visualize CIFAR-10 samples as human-readable images

    Args:
        num_samples (int): Number of images to extract and save
        save_path (str): Directory to save the images
        data_path (str): Path to data directory
    """

    # CIFAR-10 class names
    classes = ('plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck')

    # Create save directory
    os.makedirs(save_path, exist_ok=True)

    # Check if CIFAR-10 is already downloaded
    print("Checking CIFAR-10 dataset...")

    if check_cifar10_exists(data_path):
        dataset_size = get_cifar10_size(data_path)
        print(f"✓ CIFAR-10 dataset found! ({dataset_size:.1f} MB)")
        print("  Skipping download...")
        download_needed = False
    else:
        print("✗ CIFAR-10 dataset not found.")
        print("  Will download dataset (~170 MB)...")
        download_needed = True

    print("Loading CIFAR-10 dataset...")

    # Load CIFAR-10 without any normalization (just convert to tensor)
    transform_raw = transforms.Compose([
        transforms.ToTensor(),
    ])

    # Load the dataset
    trainset = torchvision.datasets.CIFAR10(root=data_path, train=True,
                                           download=download_needed, transform=transform_raw)
    
    # Create dataloader
    trainloader = torch.utils.data.DataLoader(trainset, batch_size=num_samples, 
                                             shuffle=True, num_workers=2)
    
    # Get one batch of images
    dataiter = iter(trainloader)
    images, labels = next(dataiter)
    
    print(f"Extracted {num_samples} images from CIFAR-10")
    print(f"Image shape: {images.shape}")  # Should be [num_samples, 3, 32, 32]
    
    # Create a figure to show all images
    fig, axes = plt.subplots(2, 5, figsize=(15, 6))
    fig.suptitle('CIFAR-10 Sample Images', fontsize=16)
    
    for i in range(num_samples):
        # Convert tensor to numpy and transpose from CHW to HWC
        img = images[i].numpy().transpose((1, 2, 0))
        
        # Clip values to [0, 1] range (should already be in this range)
        img = np.clip(img, 0, 1)
        
        # Get class name
        class_name = classes[labels[i]]
        
        # Plot in subplot
        row = i // 5
        col = i % 5
        axes[row, col].imshow(img)
        axes[row, col].set_title(f'{class_name}')
        axes[row, col].axis('off')
        
        # Save individual image
        plt.figure(figsize=(4, 4))
        plt.imshow(img)
        plt.title(f'CIFAR-10 Sample {i+1}: {class_name}')
        plt.axis('off')
        individual_save_path = os.path.join(save_path, f'cifar10_sample_{i+1}_{class_name}.png')
        plt.savefig(individual_save_path, bbox_inches='tight', dpi=150)
        plt.close()
        
        print(f"Saved: {individual_save_path}")
    
    # Save the combined figure
    combined_save_path = os.path.join(save_path, 'cifar10_combined_samples.png')
    fig.savefig(combined_save_path, bbox_inches='tight', dpi=150)
    plt.close()
    
    print(f"Saved combined image: {combined_save_path}")
    
    # Also create a larger version of each image (upscaled)
    print("\nCreating upscaled versions...")
    
    for i in range(num_samples):
        # Convert tensor to numpy and transpose
        img = images[i].numpy().transpose((1, 2, 0))
        img = np.clip(img, 0, 1)
        class_name = classes[labels[i]]
        
        # Create larger figure for better visibility
        plt.figure(figsize=(8, 8))
        plt.imshow(img, interpolation='nearest')  # Use nearest neighbor to keep pixelated look
        plt.title(f'CIFAR-10 Sample {i+1}: {class_name}\n(Original 32x32 pixels)', fontsize=14)
        plt.axis('off')
        
        upscaled_save_path = os.path.join(save_path, f'cifar10_large_{i+1}_{class_name}.png')
        plt.savefig(upscaled_save_path, bbox_inches='tight', dpi=150)
        plt.close()
        
        print(f"Saved upscaled: {upscaled_save_path}")
    
    print(f"\nAll images saved to: {save_path}")
    print(f"Total files created: {num_samples * 2 + 1}")  # individual + upscaled + combined

def show_dataset_info(data_path='./data'):
    """Show basic information about CIFAR-10 dataset"""

    print("="*60)
    print("CIFAR-10 DATASET INFORMATION")
    print("="*60)

    # Check if dataset exists first
    if check_cifar10_exists(data_path):
        dataset_size = get_cifar10_size(data_path)
        print(f"Dataset status: ✓ Found ({dataset_size:.1f} MB)")
        download_needed = False
    else:
        print("Dataset status: ✗ Not found (will download)")
        download_needed = True

    # Load dataset to get info
    transform = transforms.ToTensor()
    trainset = torchvision.datasets.CIFAR10(root=data_path, train=True,
                                           download=download_needed, transform=transform)
    testset = torchvision.datasets.CIFAR10(root=data_path, train=False,
                                          download=download_needed, transform=transform)
    
    print(f"Training set size: {len(trainset)} images")
    print(f"Test set size: {len(testset)} images")
    print(f"Image dimensions: 32 x 32 pixels")
    print(f"Number of channels: 3 (RGB)")
    print(f"Number of classes: 10")
    
    classes = ('plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck')
    print(f"Classes: {', '.join(classes)}")
    
    # Get one sample to show data format
    sample_img, sample_label = trainset[0]
    print(f"Sample image tensor shape: {sample_img.shape}")
    print(f"Sample label: {sample_label} ({classes[sample_label]})")
    print(f"Pixel value range: [{sample_img.min():.3f}, {sample_img.max():.3f}]")
    
    print("="*60)

if __name__ == "__main__":
    print("CIFAR-10 Image Visualization Tool")
    print("="*50)

    # Show dataset information
    show_dataset_info(data_path='./data')

    print("\nExtracting and saving sample images...")

    # Extract and save 10 sample images
    visualize_cifar10_samples(num_samples=10, save_path="./cifar10_samples", data_path='./data')

    print("\nDone! Check the './cifar10_samples' folder for the images.")
    print("\nFiles created:")
    print("- Individual images: cifar10_sample_X_classname.png")
    print("- Upscaled images: cifar10_large_X_classname.png")
    print("- Combined view: cifar10_combined_samples.png")
