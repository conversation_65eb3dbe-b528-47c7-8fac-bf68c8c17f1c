import torch
import torchvision
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import numpy as np
import os

# frog, truck, truck, deer, car, car, bird, horse, ship, cat

def check_cifar10_exists(data_path='./data'):
    """
    Check if CIFAR-10 dataset is already downloaded

    Args:
        data_path (str): Path to data directory

    Returns:
        bool: True if CIFAR-10 exists, False otherwise
    """
    cifar10_path = os.path.join(data_path, 'cifar-10-batches-py')

    # Check if the main directory exists
    if not os.path.exists(cifar10_path):
        return False

    # Check if all required files exist
    required_files = [
        'data_batch_1',
        'data_batch_2',
        'data_batch_3',
        'data_batch_4',
        'data_batch_5',
        'test_batch',
        'batches.meta'
    ]

    for file_name in required_files:
        file_path = os.path.join(cifar10_path, file_name)
        if not os.path.exists(file_path):
            print(f"Missing file: {file_path}")
            return False

    return True

def get_cifar10_size(data_path='./data'):
    """
    Get the size of CIFAR-10 dataset on disk

    Args:
        data_path (str): Path to data directory

    Returns:
        float: Size in MB, or 0 if not found
    """
    cifar10_path = os.path.join(data_path, 'cifar-10-batches-py')

    if not os.path.exists(cifar10_path):
        return 0

    total_size = 0
    for dirpath, dirnames, filenames in os.walk(cifar10_path):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            if os.path.exists(file_path):
                total_size += os.path.getsize(file_path)

    return total_size / (1024 * 1024)  # Convert to MB

def cut_and_pad_image(img_tensor):
    """
    Cut image into 4 pieces and zero-pad each piece back to original size

    Args:
        img_tensor: Tensor of shape [3, 32, 32] (C, H, W)

    Returns:
        List of 4 tensors, each [3, 32, 32] with zero padding
    """
    C, H, W = img_tensor.shape  # Should be [3, 32, 32]
    half_h, half_w = H // 2, W // 2  # 16, 16

    # Cut into 4 pieces (each 16x16)
    top_left = img_tensor[:, 0:half_h, 0:half_w]        # [3, 16, 16]
    top_right = img_tensor[:, 0:half_h, half_w:W]       # [3, 16, 16]
    bottom_left = img_tensor[:, half_h:H, 0:half_w]     # [3, 16, 16]
    bottom_right = img_tensor[:, half_h:H, half_w:W]    # [3, 16, 16]

    # Create zero-padded versions (32x32 with black padding)
    pieces = []

    # Top-left: place in top-left corner, rest is black
    padded = torch.zeros(C, H, W)
    padded[:, 0:half_h, 0:half_w] = top_left
    pieces.append(padded)

    # Top-right: place in top-right corner, rest is black
    padded = torch.zeros(C, H, W)
    padded[:, 0:half_h, half_w:W] = top_right
    pieces.append(padded)

    # Bottom-left: place in bottom-left corner, rest is black
    padded = torch.zeros(C, H, W)
    padded[:, half_h:H, 0:half_w] = bottom_left
    pieces.append(padded)

    # Bottom-right: place in bottom-right corner, rest is black
    padded = torch.zeros(C, H, W)
    padded[:, half_h:H, half_w:W] = bottom_right
    pieces.append(padded)

    return pieces

def visualize_cifar10_samples(num_samples=10, save_path="./cifar10_samples_2.0", data_path='./data'):
    """
    Extract and visualize CIFAR-10 samples as human-readable images

    Args:
        num_samples (int): Number of images to extract and save
        save_path (str): Directory to save the images
        data_path (str): Path to data directory
    """

    # CIFAR-10 class names
    classes = ('plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck')

    # Create save directory
    os.makedirs(save_path, exist_ok=True)

    # Check if CIFAR-10 is already downloaded
    print("Checking CIFAR-10 dataset...")

    if check_cifar10_exists(data_path):
        dataset_size = get_cifar10_size(data_path)
        print(f"✓ CIFAR-10 dataset found! ({dataset_size:.1f} MB)")
        print("  Skipping download...")
        download_needed = False
    else:
        print("✗ CIFAR-10 dataset not found.")
        print("  Will download dataset (~170 MB)...")
        download_needed = True

    print("Loading CIFAR-10 dataset...")

    # Load CIFAR-10 without any normalization (just convert to tensor)
    transform_raw = transforms.Compose([
        transforms.ToTensor(),
    ])

    # Load the dataset
    trainset = torchvision.datasets.CIFAR10(root=data_path, train=True,
                                           download=download_needed, transform=transform_raw)
    
    # Get first 10 images (not random)
    print(f"Selecting first {num_samples} images from dataset...")
    images = []
    labels = []

    for i in range(num_samples):
        img, label = trainset[i]  # Get image at index i (first 10)
        images.append(img)
        labels.append(label)

    # Convert to tensors
    images = torch.stack(images)  # Shape: [10, 3, 32, 32]
    labels = torch.tensor(labels)  # Shape: [10]
    
    print(f"Extracted first {num_samples} images from CIFAR-10")
    print(f"Image shape: {images.shape}")  # Should be [num_samples, 3, 32, 32]

    # Process each image: save original and 4 cut pieces
    for i in range(num_samples):
        # Get class name
        class_name = classes[labels[i]]
        print(f"Processing image {i+1}: {class_name}")

        # Convert tensor to numpy for display (CHW to HWC)
        original_img = images[i].numpy().transpose((1, 2, 0))
        original_img = np.clip(original_img, 0, 1)

        # Save original image
        plt.figure(figsize=(4, 4))
        plt.imshow(original_img)
        plt.title(f'Original {i+1}: {class_name}')
        plt.axis('off')
        original_save_path = os.path.join(save_path, f'original_{i+1}_{class_name}.png')
        plt.savefig(original_save_path, bbox_inches='tight', dpi=150)
        plt.close()

        # Cut into 4 pieces and zero-pad
        pieces = cut_and_pad_image(images[i])
        piece_names = ['top_left', 'top_right', 'bottom_left', 'bottom_right']

        # Save each piece
        for j, (piece, piece_name) in enumerate(zip(pieces, piece_names)):
            # Convert to numpy for display
            piece_img = piece.numpy().transpose((1, 2, 0))
            piece_img = np.clip(piece_img, 0, 1)

            # Save individual piece
            plt.figure(figsize=(4, 4))
            plt.imshow(piece_img)
            plt.title(f'{class_name} - {piece_name}')
            plt.axis('off')
            piece_save_path = os.path.join(save_path, f'piece_{i+1}_{class_name}_{piece_name}.png')
            plt.savefig(piece_save_path, bbox_inches='tight', dpi=150)
            plt.close()

        # Create combined view for this image (original + 4 pieces)
        fig, axes = plt.subplots(1, 5, figsize=(20, 4))
        fig.suptitle(f'Image {i+1}: {class_name} - Original and 4 Pieces', fontsize=14)

        # Show original
        axes[0].imshow(original_img)
        axes[0].set_title('Original')
        axes[0].axis('off')

        # Show 4 pieces
        for j, (piece, piece_name) in enumerate(zip(pieces, piece_names)):
            piece_img = piece.numpy().transpose((1, 2, 0))
            piece_img = np.clip(piece_img, 0, 1)
            axes[j+1].imshow(piece_img)
            axes[j+1].set_title(piece_name.replace('_', ' ').title())
            axes[j+1].axis('off')

        # Save combined view
        combined_save_path = os.path.join(save_path, f'combined_{i+1}_{class_name}.png')
        fig.savefig(combined_save_path, bbox_inches='tight', dpi=150)
        plt.close()

        print(f"  Saved original and 4 pieces for {class_name}")

    print(f"\nAll images processed and saved to: {save_path}")
    
    print(f"Total files created: {num_samples * 6}")  # original + 4 pieces + combined per image

def show_dataset_info(data_path='./data'):
    """Show basic information about CIFAR-10 dataset"""

    print("="*60)
    print("CIFAR-10 DATASET INFORMATION")
    print("="*60)

    # Check if dataset exists first
    if check_cifar10_exists(data_path):
        dataset_size = get_cifar10_size(data_path)
        print(f"Dataset status: ✓ Found ({dataset_size:.1f} MB)")
        download_needed = False
    else:
        print("Dataset status: ✗ Not found (will download)")
        download_needed = True

    # Load dataset to get info
    transform = transforms.ToTensor()
    trainset = torchvision.datasets.CIFAR10(root=data_path, train=True,
                                           download=download_needed, transform=transform)
    testset = torchvision.datasets.CIFAR10(root=data_path, train=False,
                                          download=download_needed, transform=transform)
    
    print(f"Training set size: {len(trainset)} images")
    print(f"Test set size: {len(testset)} images")
    print(f"Image dimensions: 32 x 32 pixels")
    print(f"Number of channels: 3 (RGB)")
    print(f"Number of classes: 10")
    
    classes = ('plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck')
    print(f"Classes: {', '.join(classes)}")
    
    # Get one sample to show data format
    sample_img, sample_label = trainset[0]
    print(f"Sample image tensor shape: {sample_img.shape}")
    print(f"Sample label: {sample_label} ({classes[sample_label]})")
    print(f"Pixel value range: [{sample_img.min():.3f}, {sample_img.max():.3f}]")
    
    print("="*60)

if __name__ == "__main__":
    print("CIFAR-10 Image Visualization Tool")
    print("="*50)

    # Show dataset information
    show_dataset_info(data_path='./data')

    print("\nExtracting and saving sample images...")

    # Extract and save 10 sample images
    visualize_cifar10_samples(num_samples=10, save_path="./cifar10_samples_2.0", data_path='./data')

    print("\nDone! Check the './cifar10_samples_2.0' folder for the images.")
    print("\nFiles created for each image:")
    print("- Original image: original_X_classname.png")
    print("- 4 pieces: piece_X_classname_top_left.png, piece_X_classname_top_right.png, etc.")
    print("- Combined view: combined_X_classname.png (shows original + 4 pieces)")
    print(f"\nTotal: {10 * 6} files (6 files per image × 10 images)")
